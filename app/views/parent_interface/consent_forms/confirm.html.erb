<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path, name: "edit consent page") %>
<% end %>

<% change_link = Proc.new do |attr, params = {}|
     options = attr.in?(%i[consent reason]) ? {} : { skip_to_confirm: true }
     parent_interface_consent_form_edit_path(
       @consent_form,
       attr.to_s.dasherize,
       options.merge(params)
     )
   end %>

<%= h1 "Check and confirm" %>

<% if @consent_form.consent_given_one? %>
  <% if @consent_form.chosen_vaccine == "menacwy" %>
    <%= render AppCardComponent.new do |c| %>
      <% c.with_heading { t("consent_forms.confirm.consent_card_title.menacwy") } %>
      <%= govuk_summary_list do |summary_list|
            summary_list.with_row do |row|
              row.with_key { "Decision" }
              row.with_value { "Consent given" }
              row.with_action(text: "Change",
                              href: change_link[:consent],
                              visually_hidden_text: "consent")
            end
          end %>
    <% end %>

    <%= render AppCardComponent.new do |c| %>
      <% c.with_heading { t("consent_forms.confirm.consent_card_title.td_ipv") } %>
      <%= govuk_summary_list do |summary_list|
            summary_list.with_row do |row|
              row.with_key { "Decision" }
              row.with_value { "Consent refused" }
              row.with_action(text: "Change",
                              href: change_link[:consent],
                              visually_hidden_text: "consent")
            end
          
            reason_notes = @consent_form.reason_notes.present? ? "<br>".html_safe + @consent_form.reason_notes : ""
            reason_for_refusal_text = @consent_form.human_enum_name(:reason) + reason_notes
          
            summary_list.with_row do |row|
              row.with_key { "Reason" }
              row.with_value { reason_for_refusal_text.html_safe }
              row.with_action(text: "Change",
                              href: change_link[:reason],
                              visually_hidden_text: "reason for refusal")
            end
          end %>
    <% end %>
  <% else %>
    <%= render AppCardComponent.new do |c| %>
      <% c.with_heading { t("consent_forms.confirm.consent_card_title.td_ipv") } %>
      <%= govuk_summary_list do |summary_list|
            summary_list.with_row do |row|
              row.with_key { "Decision" }
              row.with_value { "Consent given" }
              row.with_action(text: "Change",
                              href: change_link[:consent],
                              visually_hidden_text: "consent")
            end
          end %>
    <% end %>

    <%= render AppCardComponent.new do |c| %>
      <% c.with_heading { t("consent_forms.confirm.consent_card_title.menacwy") } %>
      <%= govuk_summary_list do |summary_list|
            summary_list.with_row do |row|
              row.with_key { "Decision" }
              row.with_value { "Consent refused" }
              row.with_action(text: "Change",
                              href: change_link[:consent],
                              visually_hidden_text: "consent")
            end
          
            reason_notes = @consent_form.reason_notes.present? ? "<br>".html_safe + @consent_form.reason_notes : ""
            reason_for_refusal_text = @consent_form.human_enum_name(:reason) + reason_notes
          
            summary_list.with_row do |row|
              row.with_key { "Reason" }
              row.with_value { reason_for_refusal_text.html_safe }
              row.with_action(text: "Change",
                              href: change_link[:reason],
                              visually_hidden_text: "reason for refusal")
            end
          end %>
    <% end %>
  <% end %>
<% else %>
  <%= render AppCardComponent.new do |c| %>
    <% programme_type = @consent_form.programmes.first.type.in?(["menacwy", "td_ipv"]) ? "menacwy_td_ipv" : @consent_form.programmes.first.type %>
    <% c.with_heading { t("consent_forms.confirm.consent_card_title.#{programme_type}") } %>
    <%= govuk_summary_list do |summary_list|
          summary_list.with_row do |row|
            row.with_key { "Decision" }
            row.with_value {
              @consent_form.consent_given? ? "Consent given" : "Consent refused"
            }
            row.with_action(text: "Change",
                            href: change_link[:consent],
                            visually_hidden_text: "consent")
          end
        
          if @consent_form.consent_refused?
            reason_notes = @consent_form.reason_notes.present? ? "<br>".html_safe + @consent_form.reason_notes : ""
            reason_for_refusal_text = @consent_form.human_enum_name(:reason) + reason_notes
        
            summary_list.with_row do |row|
              row.with_key { "Reason" }
              row.with_value { reason_for_refusal_text.html_safe }
              row.with_action(text: "Change",
                              href: change_link[:reason],
                              visually_hidden_text: "reason for refusal")
            end
          end
        end %>
  <% end %>
<% end %>

<%= render AppCardComponent.new do |c| %>
  <% c.with_heading { "About your child" } %>
  <%= govuk_summary_list do |summary_list|
        summary_list.with_row do |row|
          row.with_key { "Child’s name" }
          row.with_value { @consent_form.full_name(context: :parents) }
          row.with_action(text: "Change",
                          href: change_link[:name],
                          visually_hidden_text: "child’s name")
        end
      
        if @consent_form.use_preferred_name
          summary_list.with_row do |row|
            row.with_key { "Also known as" }
            row.with_value { @consent_form.preferred_full_name(context: :parents) }
            row.with_action(text: "Change",
                            href: change_link[:name],
                            visually_hidden_text: "common name")
          end
        end
      
        summary_list.with_row do |row|
          row.with_key { "Date of birth" }
          row.with_value { @consent_form.date_of_birth.to_fs(:long) }
          row.with_action(
            text: "Change",
            href: change_link[:date_of_birth],
            visually_hidden_text: "date of birth",
          )
        end
      
        if @consent_form.consent_given? || @consent_form.consent_given_one?
          summary_list.with_row do |row|
            row.with_key { "Address" }
            row.with_value { format_address_multi_line(@consent_form) }
            row.with_action(
              text: "Change",
              href: change_link[:address],
              visually_hidden_text: "address",
            )
          end
        end
      
        school = if @consent_form.education_setting_school?
            @consent_form.school.name
          elsif @consent_form.education_setting_home?
            "Home-schooled"
          else
            "Not in education"
          end
      
        summary_list.with_row do |row|
          row.with_key { "School" }
          row.with_value { school }
          row.with_action(
            text: "Change",
            href: change_link[:school],
            visually_hidden_text: "school",
          )
        end
      end %>
<% end %>

<%= render AppCardComponent.new do |c| %>
  <% c.with_heading { "About you" } %>
  <%= govuk_summary_list do |summary_list|
        summary_list.with_row do |row|
          row.with_key { "Your name" }
          row.with_value { @consent_form.parent_full_name }
          row.with_action(text: "Change",
                          href: change_link[:parent],
                          visually_hidden_text: "your name")
        end
      
        summary_list.with_row do |row|
          row.with_key { "Relationship" }
          row.with_value { @consent_form.parent_relationship.label }
          row.with_action(text: "Change",
                          href: change_link[:parent],
                          visually_hidden_text: "your relationship")
        end
      
        summary_list.with_row do |row|
          row.with_key { "Email address" }
          row.with_value { @consent_form.parent_email }
          row.with_action(text: "Change",
                          href: change_link[:parent],
                          visually_hidden_text: "your email")
        end
      
        if @consent_form.parent_phone.present?
          summary_list.with_row do |row|
            row.with_key { "Phone number" }
            row.with_value { @consent_form.parent_phone }
            row.with_action(text: "Change",
                            href: change_link[:parent],
                            visually_hidden_text: "your phone")
          end
      
          if @consent_form.parent_contact_method_type.present?
            summary_list.with_row do |row|
              row.with_key { "Phone contact method" }
              row.with_value { @consent_form.parent.contact_method_description }
              row.with_action(text: "Change",
                              href: change_link[:contact_method],
                              visually_hidden_text: "your phone contact method")
            end
          end
        else
          summary_list.with_row do |row|
            row.with_key { "Phone number" }
            row.with_value { "Not provided" }
            row.with_action(text: "Change",
                            href: change_link[:parent],
                            visually_hidden_text: "your phone")
          end
        end
      end %>
<% end %>

<% if @consent_form.consent_given? || @consent_form.consent_given_one? %>
  <%= render AppCardComponent.new do |c| %>
    <% c.with_heading { "Health questions" } %>
    <%= govuk_summary_list classes: "app-summary-list--full-width" do |summary_list|
          @consent_form.each_health_answer do |ha|
            summary_list.with_row do |row|
              row.with_key { ha.question }
              row.with_value { health_answer_response(ha) }
              row.with_action(text: "Change",
                              href: change_link[:health_question, question_number: ha.id],
                              visually_hidden_text: "your answer to health question #{ha.id + 1}")
            end
          end
        end %>
  <% end %>
<% end %>

<%= govuk_button_to "Confirm",
                    url_for(action: :record),
                    method: :put,
                    prevent_double_click: true %>
