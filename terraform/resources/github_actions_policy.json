{"Version": "2012-10-17", "Statement": [{"Sid": "Statement1", "Effect": "Allow", "Action": ["acm:DeleteCertificate", "acm:RequestCertificate", "application-autoscaling:DeleteScalingPolicy", "application-autoscaling:DeregisterScalableTarget", "application-autoscaling:PutScalingPolicy", "application-autoscaling:RegisterScalableTarget", "application-autoscaling:TagResource", "application-autoscaling:UntagResource", "codedeploy:CreateApplication", "codedeploy:CreateDeployment", "codedeploy:CreateDeploymentGroup", "codedeploy:DeleteApplication", "codedeploy:DeleteDeploymentGroup", "codedeploy:RegisterApplicationRevision", "codedeploy:UpdateDeploymentGroup", "codedeploy:UpdateApplication", "dynamodb:PutItem", "dynamodb:DeleteItem", "ec2:AllocateAddress", "ec2:AssociateRouteTable", "ec2:AttachInternetGateway", "ec2:AuthorizeSecurityGroupEgress", "ec2:AuthorizeSecurityGroupIngress", "ec2:CreateFlowLogs", "ec2:CreateInternetGateway", "ec2:CreateNatGateway", "ec2:CreateRoute", "ec2:CreateRouteTable", "ec2:CreateSecurityGroup", "ec2:CreateSubnet", "ec2:CreateVpc", "ec2:DeleteFlowLogs", "ec2:DeleteInternetGateway", "ec2:DeleteNatGateway", "ec2:DeleteRoute", "ec2:DeleteRouteTable", "ec2:DeleteSecurityGroup", "ec2:DeleteSubnet", "ec2:DeleteVpc", "ec2:DetachInternetGateway", "ec2:DetachNetworkInterface", "ec2:DisassociateAddress", "ec2:DisassociateRouteTable", "ec2:ModifyVpcAttribute", "ec2:ReleaseAddress", "ec2:RevokeSecurityGroupEgress", "ec2:RevokeSecurityGroupIngress", "ecr:CompleteLayerUpload", "ecr:InitiateLayerUpload", "ecr:PutImage", "ecr:UploadLayerPart", "ecs:CreateCluster", "ecs:CreateService", "ecs:DeleteCluster", "ecs:DeleteService", "ecs:DeregisterTaskDefinition", "ecs:RegisterTaskDefinition", "ecs:UpdateCluster", "ecs:UpdateService", "elasticloadbalancing:CreateListener", "elasticloadbalancing:CreateLoadBalancer", "elasticloadbalancing:CreateRule", "elasticloadbalancing:CreateTargetGroup", "elasticloadbalancing:DeleteListener", "elasticloadbalancing:DeleteLoadBalancer", "elasticloadbalancing:DeleteRule", "elasticloadbalancing:DeleteTargetGroup", "elasticloadbalancing:ModifyListener", "elasticloadbalancing:ModifyListenerAttributes", "elasticloadbalancing:ModifyLoadBalancerAttributes", "elasticloadbalancing:ModifyRule", "elasticloadbalancing:ModifyTargetGroupAttributes", "elasticloadbalancing:AddListenerCertificates", "elasticloadbalancing:RemoveListenerCertificates", "iam:AttachRolePolicy", "iam:CreatePolicyVersion", "iam:PassRole", "iam:CreateRole", "iam:CreateServiceLinkedRole", "iam:CreatePolicy", "iam:DeleteRole", "iam:DeletePolicy", "iam:DeletePolicyVersion", "iam:DetachRolePolicy", "kms:CreateGrant", "kms:Decrypt", "logs:CreateLogGroup", "logs:DeleteLogGroup", "logs:PutRetentionPolicy", "rds:CreateDBCluster", "rds:CreateDBInstance", "rds:CreateDBSubnetGroup", "rds:DeleteDBCluster", "rds:DeleteDBInstance", "rds:DeleteDBSubnetGroup", "rds:ModifyDBCluster", "rds:ModifyCurrentDBClusterCapacity", "rds:ModifyDBInstance", "resource-groups:CreateGroup", "resource-groups:DeleteGroup", "route53:ChangeResourceRecordSets", "route53:CreateHostedZone", "s3:CreateBucket", "s3:DeleteBucket", "s3:DeleteBucketPolicy", "s3:DeleteObject", "s3:DeleteObjectVersion", "s3:PutBucketLogging", "s3:PutBucketPolicy", "s3:PutBucketPublicAccessBlock", "s3:PutBucketVersioning", "s3:PutObject", "secretsmanager:<PERSON><PERSON><PERSON><PERSON><PERSON>", "secretsmanager:PutSecretV<PERSON>ue", "secretsmanager:UpdateSecret", "ssm:DeleteParameter", "ssm:DeleteParameters", "ssm:PutParameter"], "Resource": ["*"]}]}